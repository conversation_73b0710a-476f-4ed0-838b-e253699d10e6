'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2, Image as ImageIcon, TextIcon, VideoIcon, FileTextIcon, MonitorPlayIcon } from 'lucide-react';
import { FileUploader } from '@/components/file-uploader';
import Image from 'next/image';
import { toast } from 'sonner';

// Define the types for content blocks
export type ContentBlock = {
  id: string;
  type: 'text' | 'image' | 'video' | 'pdf' | 'zoom-recording';
  value: string; // For text content, image URL, video URL, PDF URL, Zoom recording URL
};

interface DynamicContentEditorProps {
  initialContent: ContentBlock[];
  onContentChange: (content: ContentBlock[]) => void;
  allowImages?: boolean;
  placeholder?: string;
}

export function DynamicContentEditor({
  initialContent,
  onContentChange,
  allowImages = true,
  placeholder,
}: DynamicContentEditorProps) {
  const [content, setContent] = useState<ContentBlock[]>(initialContent);

  React.useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const addBlock = (type: ContentBlock['type']) => {
    const newBlock: ContentBlock = {
      id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      value: '',
    };
    const updatedContent = [...content, newBlock];
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const updateBlock = (id: string, newValue: string) => {
    const updatedContent = content.map((block) =>
      block.id === id ? { ...block, value: newValue } : block
    );
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const removeBlock = (id: string) => {
    const updatedContent = content.filter((block) => block.id !== id);
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const handleImageUpload = useCallback(async (files: File[], blockId: string) => {
    if (!files || files.length === 0) {
      toast.error('No file selected for upload.');
      return;
    }

    const file = files[0];
    toast.info(`Uploading ${file.name}...`);

    try {
      const response = await fetch(`/api/upload?filename=${file.name}`, {
        method: 'POST',
        body: file,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const newBlob = await response.json();
      updateBlock(blockId, newBlob.url);
      toast.success('Image uploaded successfully!');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(`Failed to upload image: ${(error as Error).message}`);
    }
  }, [updateBlock]);

  return (
    <div className="space-y-4">
      {content.map((block) => (
        <Card key={block.id} className="relative p-4 mb-4">
          {block.type === 'text' ? (
            <Textarea
              placeholder={placeholder || "Enter text content"}
              value={block.value}
              onChange={(e) => updateBlock(block.id, e.target.value)}
              rows={Math.max(3, Math.ceil(block.value.length / 80))} // Adjust rows dynamically
              className="min-h-[80px]"
            />
          ) : block.type === 'image' ? (
            <div className="space-y-2">
              {block.value ? (
                <div className="relative w-full h-48 border rounded-md overflow-hidden">
                  <Image
                    src={block.value}
                    alt="Uploaded content"
                    layout="fill"
                    objectFit="contain"
                    className="rounded-md"
                  />
                </div>
              ) : (
                <FileUploader
                  onUpload={(files) => handleImageUpload(files, block.id)}
                  maxFiles={1}
                  accept={{ 'image/*': [] }}
                />
              )}
            </div>
          ) : (
            // Handle video, pdf, zoom-recording by displaying the URL
            <Textarea
              placeholder={`Enter ${block.type} URL`}
              value={block.value}
              onChange={(e) => updateBlock(block.id, e.target.value)}
              rows={3}
            />
          )}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 text-muted-foreground hover:text-destructive"
            onClick={() => removeBlock(block.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </Card>
      ))}
      <div className="flex flex-wrap gap-2 pt-2">
        <Button variant="outline" onClick={() => addBlock('text')} size="sm">
          <TextIcon className="h-4 w-4 mr-2" /> Add Text Block
        </Button>
        {allowImages && (
          <>
            <Button variant="outline" onClick={() => addBlock('image')} size="sm">
              <ImageIcon className="h-4 w-4 mr-2" /> Add Image Block
            </Button>
            <Button variant="outline" onClick={() => addBlock('video')} size="sm">
              <MonitorPlayIcon className="h-4 w-4 mr-2" /> Add Video Block
            </Button>
            <Button variant="outline" onClick={() => addBlock('pdf')} size="sm">
              <FileTextIcon className="h-4 w-4 mr-2" /> Add PDF Block
            </Button>
            <Button variant="outline" onClick={() => addBlock('zoom-recording')} size="sm">
              <VideoIcon className="h-4 w-4 mr-2" /> Add Zoom Recording Block
            </Button>
          </>
        )}
      </div>
    </div>
  );
}